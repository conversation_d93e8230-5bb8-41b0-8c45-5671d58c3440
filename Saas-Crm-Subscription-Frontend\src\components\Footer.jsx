import React from 'react';
import {
    Mail,
    Phone,
    MapPin,
    Facebook,
    Twitter,
    Linkedin,
    Instagram,
    ArrowRight
} from 'lucide-react';
import { Button } from './ui/button';
import { Input } from './ui/input';

const Footer = () => {
    const footerSections = [
        {
            title: 'Product',
            links: [
                { name: 'Features', href: '#features' },
                { name: 'Pricing', href: '#pricing' },
                { name: 'Integrations', href: '#integrations' },
                { name: 'API', href: '#api' },
                { name: 'Security', href: '#security' }
            ]
        },
        {
            title: 'Solutions',
            links: [
                { name: 'Small Business', href: '#small-business' },
                { name: 'Enterprise', href: '#enterprise' },
                { name: 'Sales Teams', href: '#sales' },
                { name: 'Marketing', href: '#marketing' },
                { name: 'Customer Support', href: '#support' }
            ]
        },
        {
            title: 'Resources',
            links: [
                { name: 'Documentation', href: '#docs' },
                { name: 'Help Center', href: '#help' },
                { name: 'Blog', href: '#blog' },
                { name: 'Webinars', href: '#webinars' },
                { name: 'Case Studies', href: '#cases' }
            ]
        },
        {
            title: 'Company',
            links: [
                { name: 'About Us', href: '#about' },
                { name: 'Careers', href: '#careers' },
                { name: 'Press', href: '#press' },
                { name: 'Partners', href: '#partners' },
                { name: 'Contact', href: '#contact' }
            ]
        }
    ];

    const socialLinks = [
        { icon: Facebook, href: '#', label: 'Facebook' },
        { icon: Twitter, href: '#', label: 'Twitter' },
        { icon: Linkedin, href: '#', label: 'LinkedIn' },
        { icon: Instagram, href: '#', label: 'Instagram' }
    ];

    const handleLinkClick = (href) => {
        if (href.startsWith('#')) {
            const element = document.querySelector(href);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
            }
        }
    };

    return (
        <footer className="bg-gray-900 text-white">
            {/* <div className="border-b border-gray-800">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                        <div>
                            <h3 className="text-2xl font-bold mb-2">Stay Updated</h3>
                            <p className="text-gray-400">
                                Get the latest updates, tips, and insights delivered to your inbox.
                            </p>
                        </div>
                        <div className="flex flex-col sm:flex-row gap-4">
                            <Input
                                type="email"
                                placeholder="Enter your email"
                                className="bg-gray-800 border-gray-700 text-white placeholder-gray-400 flex-1"
                            />
                            <Button className="bg-blue-600 hover:bg-blue-700 whitespace-nowrap">
                                Subscribe
                                <ArrowRight className="ml-2 h-4 w-4" />
                            </Button>
                        </div>
                    </div>
                </div>
            </div> */}

            {/* <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
                    <div className="lg:col-span-2">
                        <div className="flex items-center mb-4">
                            <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                                <span className="text-white font-bold text-lg">A</span>
                            </div>
                            <span className="text-xl font-bold">Accord</span>
                        </div>
                        <p className="text-gray-400 mb-6 max-w-sm">
                            Empowering businesses worldwide with intelligent CRM solutions that drive growth,
                            enhance customer relationships, and streamline operations.
                        </p>

                        <div className="space-y-3">
                            <div className="flex items-center text-gray-400">
                                <Mail className="h-4 w-4 mr-3" />
                                <span><EMAIL></span>
                            </div>
                            <div className="flex items-center text-gray-400">
                                <Phone className="h-4 w-4 mr-3" />
                                <span>+****************</span>
                            </div>
                            <div className="flex items-center text-gray-400">
                                <MapPin className="h-4 w-4 mr-3" />
                                <span>San Francisco, CA</span>
                            </div>
                        </div>
                    </div>

                    {footerSections.map((section) => (
                        <div key={section.title}>
                            <h4 className="text-lg font-semibold mb-4">{section.title}</h4>
                            <ul className="space-y-3">
                                {section.links.map((link) => (
                                    <li key={link.name}>
                                        <button
                                            onClick={() => handleLinkClick(link.href)}
                                            className="text-gray-400 hover:text-white transition-colors text-left"
                                        >
                                            {link.name}
                                        </button>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    ))}
                </div>
            </div> */}

            {/* Bottom Footer */}
            <div className="border-t border-gray-800">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                    <div className="flex flex-col md:flex-row justify-between items-center">
                        <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6">
                            <p className="text-white text-sm">
                                © 2024 Accord. All rights reserved.
                            </p>
                            {/* <div className="flex space-x-6">
                                <button className="text-gray-400 hover:text-white text-sm transition-colors">
                                    Privacy Policy
                                </button>
                                <button className="text-gray-400 hover:text-white text-sm transition-colors">
                                    Terms of Service
                                </button>
                                <button className="text-gray-400 hover:text-white text-sm transition-colors">
                                    Cookie Policy
                                </button>
                            </div> */}
                        </div>

                        {/* <div className="flex space-x-4 mt-4 md:mt-0">
                            {socialLinks.map((social) => (
                                <a
                                    key={social.label}
                                    href={social.href}
                                    className="text-gray-400 hover:text-white transition-colors"
                                    aria-label={social.label}
                                >
                                    <social.icon className="h-5 w-5" />
                                </a>
                            ))}
                        </div> */}
                    </div>
                </div>
            </div>
        </footer>
    );
};

export default Footer;
