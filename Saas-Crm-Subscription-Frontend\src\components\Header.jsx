import React, { useState } from 'react';
import { Menu, X, ChevronDown, LogIn } from 'lucide-react';
import { Button } from './ui/button';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from './ui/dropdown-menu';

const Header = () => {
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

    const navigationItems = [
        {
            name: 'Services',
            items: [
                { name: 'CRM Solutions', href: '#crm' },
                { name: 'Sales Automation', href: '#sales' },
                { name: 'Customer Support', href: '#support' },
                { name: 'Marketing Tools', href: '#marketing' },
                { name: 'Analytics & Reports', href: '#analytics' }
            ]
        },
        {
            name: 'Solutions',
            items: [
                { name: 'Small Business', href: '#small-business' },
                { name: 'Enterprise', href: '#enterprise' },
                { name: 'E-commerce', href: '#ecommerce' },
                { name: 'Real Estate', href: '#real-estate' },
                { name: 'Healthcare', href: '#healthcare' }
            ]
        },
        {
            name: 'Resources',
            items: [
                { name: 'Documentation', href: '#docs' },
                { name: 'API Reference', href: '#api' },
                { name: 'Help Center', href: '#help' },
                { name: 'Video Tutorials', href: '#tutorials' },
                { name: 'Webinars', href: '#webinars' }
            ]
        },
        { name: 'Pricing', href: '#pricing' },
        { name: 'About', href: '#about' },
        { name: 'Contact', href: '#contact' }
    ];

    const handleNavClick = (href) => {
        if (href.startsWith('#')) {
            const element = document.querySelector(href);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
            }
        }
        setIsMobileMenuOpen(false);
    };

    return (
        <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between items-center h-16">
                    {/* Logo */}
                    <div className="flex items-center">
                        <div className="flex-shrink-0 flex items-center">
                            <div className="h-8 w-8 bg-accent rounded-lg flex items-center justify-center mr-3">
                                <span className="text-white font-bold text-lg">A</span>
                            </div>
                            <span className="text-xl font-bold text-gray-900">Accord</span>
                        </div>
                    </div>

                    {/* Desktop Navigation */}
                    <nav className="hidden md:flex space-x-8">
                        {navigationItems.map((item) => (
                            item.items ? (
                                <DropdownMenu key={item.name}>
                                    <DropdownMenuTrigger asChild>
                                        <button className="flex items-center text-gray-700 hover:text-accent px-3 py-2 text-sm font-medium transition-colors">
                                            {item.name}
                                            <ChevronDown className="ml-1 h-4 w-4" />
                                        </button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="start" className="w-48">
                                        {item.items.map((subItem) => (
                                            <DropdownMenuItem
                                                key={subItem.name}
                                                onClick={() => handleNavClick(subItem.href)}
                                                className="cursor-pointer"
                                            >
                                                {subItem.name}
                                            </DropdownMenuItem>
                                        ))}
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            ) : (
                                <button
                                    key={item.name}
                                    onClick={() => handleNavClick(item.href)}
                                    className="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors"
                                >
                                    {item.name}
                                </button>
                            )
                        ))}
                    </nav>

                    {/* Login Button */}
                    <div className="hidden md:flex items-center space-x-4">
                        <Button
                            onClick={() => window.location.href = import.meta.env.VITE_API_LOGIN_URL}
                            variant="outline"
                            className="flex items-center"
                        >
                            <LogIn className="h-4 w-4 mr-2" />
                            Login
                        </Button>
                        <Button className="bg-accent hover:bg-primary">
                            Get Started
                        </Button>
                    </div>

                    {/* Mobile menu button */}
                    <div className="md:hidden">
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                        >
                            {isMobileMenuOpen ? (
                                <X className="h-6 w-6" />
                            ) : (
                                <Menu className="h-6 w-6" />
                            )}
                        </Button>
                    </div>
                </div>

                {/* Mobile Navigation */}
                {isMobileMenuOpen && (
                    <div className="md:hidden">
                        <div className="px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-200">
                            {navigationItems.map((item) => (
                                <div key={item.name}>
                                    {item.items ? (
                                        <div className="space-y-1">
                                            <div className="text-gray-900 font-medium px-3 py-2 text-sm">
                                                {item.name}
                                            </div>
                                            {item.items.map((subItem) => (
                                                <button
                                                    key={subItem.name}
                                                    onClick={() => handleNavClick(subItem.href)}
                                                    className="block text-gray-600 hover:text-blue-600 px-6 py-2 text-sm w-full text-left transition-colors"
                                                >
                                                    {subItem.name}
                                                </button>
                                            ))}
                                        </div>
                                    ) : (
                                        <button
                                            onClick={() => handleNavClick(item.href)}
                                            className="block text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium w-full text-left transition-colors"
                                        >
                                            {item.name}
                                        </button>
                                    )}
                                </div>
                            ))}
                            <div className="pt-4 pb-2 border-t border-gray-200 space-y-2">
                                <Button
                                    onClick={() => window.location.href = import.meta.env.VITE_API_LOGIN_URL}
                                    variant="outline"
                                    className="w-full flex items-center justify-center"
                                >
                                    <LogIn className="h-4 w-4 mr-2" />
                                    Login
                                </Button>
                                <Button className="w-full bg-accent hover:bg-primary">
                                    Get Started
                                </Button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </header>
    );
};

export default Header;
